const { PrismaClient } = require('@prisma/client');
const SortOrderHelper = require('../utils/sortOrderHelper');

const prisma = new PrismaClient();

const CategoryModel = {
  async createCategory(data) {
    const existingCategory = await prisma.categories.findFirst({
      where: {
        title: data.title
      }
    });
    if (existingCategory) {
      throw new Error('A category with this title already exists');
    }

    // Auto-assign sort_order if not provided
    const sortOrder = data.sort_order !== undefined ?
      data.sort_order :
      await SortOrderHelper.getNextCategorySortOrder();

    return await prisma.categories.create({
      data: {
        title: data.title,
        description: data.description,
        color: data.color,
        image: data.image,
        thumbnail: data.thumbnail,
        is_featured: data.is_featured ? true: false,
        is_premium: data.is_premium ? true: false,
        sort_order: sortOrder,
        seo_title: data.seo_title,
        seo_description: data.seo_description,
        seo_keywords: data.seo_keywords,
        translations: {
          create: data.translations?.map(translation => ({
            language: translation.language,
            title: translation.title,
            description: translation.description,
            seo_title: translation.seo_title,
            seo_description: translation.seo_description,
            seo_keywords: translation.seo_keywords
          })) || []
        }
      },
      include: {
        translations: true
      }
    });
  },

  async findCategoryById(id, query) {
    return await prisma.categories.findUnique({
      where: { id },
      include: {
        translations: query.translations ? true : false,
        sub_categories: query.sub_categories ? true : false,
        requests: query.requests ? true : false,
      }
    });
  },

  async findCategoryByTitle(title) {
    return await prisma.categories.findFirst({
      where: { title },
      include: {
        translations: true
      }
    });
  },

  async updateCategory(id, data) {
    // Handle sort order change if provided
    if (data.sort_order !== undefined) {
      await SortOrderHelper.updateCategorySortOrder(id, data.sort_order);
    }

    // First handle the translations if they exist
    if (data.translations) {
      // Delete existing translations
      await prisma.category_translations.deleteMany({
        where: { category_id: id }
      });

      // Create new translations
      await prisma.category_translations.createMany({
        data: data.translations.map(t => ({
          ...t,
          category_id: id
        }))
      });
    }

    // Update the main category fields (excluding sort_order as it's handled above)
    const updateData = {
      title: data.title,
      description: data.description,
      color: data.color,
      image: data.image,
      thumbnail: data.thumbnail,
      is_featured: data.is_featured === true || data.is_featured === '1' || data.is_featured === 'true',
      is_premium: data.is_premium === true || data.is_premium === '1' || data.is_premium === 'true',
      is_active: data.is_active === true || data.is_active === '1' || data.is_active === 'true',
      seo_title: data.seo_title,
      seo_description: data.seo_description,
      seo_keywords: data.seo_keywords,
      updated_at: new Date()
    };

    // Don't include sort_order in updateData since it's handled separately above

    return await prisma.categories.update({
      where: { id },
      data: updateData,
      include: {
        translations: true
      }
    });
  },

  async softDeleteCategory(id) {
    const result = await prisma.categories.update({
      where: { id },
      data: {
        is_deleted: true,
        updated_at: new Date()
      }
    });

    // Reorder remaining categories to fill the gap
    await SortOrderHelper.reorderCategories(id);

    return result;
  },

  async deleteCategory(id) {
    // First delete translations
    await prisma.category_translations.deleteMany({
      where: { category_id: id }
    });

    // Then delete the category
    return await prisma.categories.delete({
      where: { id }
    });
  },

  async getAllCategories(filters = {}) {
    const {
      search,
      featured,
      premium,
      sort_by = 'sort_order',
      sort_order = 'asc',
      include_deleted = false
    } = filters;

    // Build the where clause
    const whereClause = {
      AND: [
        include_deleted ? {} : { is_deleted: false }
      ]
    };

    // Add search filter if provided
    if (search) {
      whereClause.AND.push({
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      });
    }

    // Add featured filter if provided
    if (featured !== undefined) {
      whereClause.AND.push({ is_featured: featured });
    }

    // Add premium filter if provided
    if (premium !== undefined) {
      whereClause.AND.push({ is_premium: premium });
    }

    // Determine sort field and direction
    const orderBy = {};
    orderBy[sort_by] = sort_order;

    return await prisma.categories.findMany({
      where: whereClause,
      include: {
        translations: true,
        sub_categories: true
      },
      orderBy
    });
  },

  async getAllCategoriesPaginated(filters = {}, page = 1, limit = 10) {
    const {
      search,
      featured,
      premium,
      is_active,
      sort_by = 'sort_order',
      sort_order = 'asc',
      include_deleted = false
    } = filters;

    // Build the where clause
    const whereClause = {
      AND: [
        include_deleted ? {} : { is_deleted: false }
      ]
    };

    // Add search filter if provided
    if (search) {
      whereClause.AND.push({
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      });
    }

    // Add featured filter if provided
    if (featured !== undefined) {
      whereClause.AND.push({ is_featured: featured });
    }

    // Add premium filter if provided
    if (premium !== undefined) {
      whereClause.AND.push({ is_premium: premium });
    }

    // Add is_active filter if provided
    if (is_active !== undefined) {
      whereClause.AND.push({ is_active: is_active });
    }

    // Determine sort field and direction
    const orderBy = {};
    orderBy[sort_by] = sort_order;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count for pagination metadata
    const total = await prisma.categories.count({
      where: whereClause
    });

    // Get paginated data
    const data = await prisma.categories.findMany({
      where: whereClause,
      include: {
        translations: true,
        sub_categories: {
          where: { is_deleted: false },
          orderBy: { sort_order: 'asc' }
        }
      },
      orderBy,
      skip,
      take: limit
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage,
        hasPrevPage,
        count: data.length
      }
    };
  },

  async getFeaturedCategories() {
    return await prisma.categories.findMany({
      where: {
        is_featured: true,
        is_deleted: false
      },
      include: {
        translations: true
      },
      orderBy: {
        sort_order: 'asc'
      }
    });
  },

  async getPremiumCategories() {
    return await prisma.categories.findMany({
      where: {
        is_premium: true,
        is_deleted: false
      },
      include: {
        translations: true
      },
      orderBy: {
        sort_order: 'asc'
      }
    });
  },
  async searchCategoriesByName(name) {
    return await prisma.categories.findMany({
      where: {
        title: {
          contains: name,
          mode: 'insensitive'
        },
        is_deleted: false
      },
      include: {
        translations: true
      }
    });
  },

  async getCategoryWithSubcategories(id) {
    return await prisma.categories.findUnique({
      where: { id },
      include: {
        sub_categories: true,
        translations: true
      }
    });
  },

  async searchCategoriesByTitle(title) {
    return await prisma.categories.findMany({
      where: {
        title: {
          contains: title,
          mode: 'insensitive'
        },
        is_deleted: false
      },
      include: {
        translations: true
      }
    });
  },

  async getCategoryTranslations(categoryId) {
    return await prisma.category_translations.findMany({
      where: { category_id: categoryId }
    });
  },

  async addCategoryTranslation(categoryId, translationData) {
    return await prisma.category_translations.create({
      data: {
        ...translationData,
        category_id: categoryId
      }
    });
  }
};

module.exports = CategoryModel;