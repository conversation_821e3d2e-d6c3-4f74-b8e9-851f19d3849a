const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createCartTables() {
  try {
    console.log('Starting to create cart tables...');

    // Check database connection
    const databaseVersion = await prisma.$queryRaw`SELECT version()`;
    console.log('Connected to database:', databaseVersion);

    // Check if users table exists
    const userTableCheck = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `;
    console.log('Users table exists:', userTableCheck);

    // Check if offers table exists
    const offerTableCheck = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'offers'
      );
    `;
    console.log('Offers table exists:', offerTableCheck);

    // Create carts table
    console.log('Creating carts table...');
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS carts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        buyer_id TEXT NOT NULL REFERENCES users(id),
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        UNIQUE(buyer_id)
      );
    `;
    console.log('✅ Carts table created successfully');

    // Create cart_items table
    console.log('Creating cart_items table...');
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS cart_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        cart_id UUID NOT NULL REFERENCES carts(id) ON DELETE CASCADE,
        offer_id TEXT NOT NULL REFERENCES offers(id),
        quantity INTEGER NOT NULL DEFAULT 1,
        price FLOAT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        UNIQUE(cart_id, offer_id)
      );
    `;
    console.log('✅ Cart items table created successfully');

    // Verify tables were created
    const cartsTableCheck = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'carts'
      );
    `;
    console.log('Carts table exists after creation:', cartsTableCheck);

    const cartItemsTableCheck = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'cart_items'
      );
    `;
    console.log('Cart items table exists after creation:', cartItemsTableCheck);

    console.log('✅ All cart tables created successfully!');
  } catch (error) {
    console.error('❌ Error creating cart tables:', error);
    console.error('Error details:', error.message);
    if (error.meta) {
      console.error('Error metadata:', error.meta);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
createCartTables()
  .then(() => console.log('Script completed'))
  .catch(e => console.error('Script failed:', e));
