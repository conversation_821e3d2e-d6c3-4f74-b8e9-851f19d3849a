const CategoryService = require('../services/categoryService');
const { validationResult } = require('express-validator');
const { saveBufferToFile } = require('../utils/fileUpload');
const sendResponse = require('../utils/sendResponse');

class CategoryController {

  static parseCategoryFormData(data) {
    const parsed = {
      ...data,
      is_featured: data.is_featured === '1' || data.is_featured === 'true' || data.is_featured === true,
      is_premium: data.is_premium === '1' || data.is_premium === 'true' || data.is_premium === true,
      translations: data.translations ? JSON.parse(data.translations) : null,
    };

    // Only include sort_order if it's provided and valid
    if (data.sort_order && data.sort_order !== '') {
      parsed.sort_order = parseInt(data.sort_order);
    }

    return parsed;
  }

  static async createCategory(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = {};
      errors.array().forEach(error => {
        if (!formattedErrors[error.path]) {
          formattedErrors[error.path] = [];
        }
        formattedErrors[error.path] = error.msg;
      });

      return sendResponse(res, false, 'Validation failed', null, formattedErrors, null, 422);
    }

    try {
      const parsedData = CategoryController.parseCategoryFormData(req.body);
      const files = req.files;

      if (files?.image?.[0]) {
        parsedData.image = saveBufferToFile(files.image[0].buffer, files.image[0].originalname, 'uploads/categories');
      }

      if (files?.thumbnail?.[0]) {
        parsedData.thumbnail = saveBufferToFile(files.thumbnail[0].buffer, files.thumbnail[0].originalname, 'uploads/categories');
      }

      const category = await CategoryService.create(parsedData);
      return sendResponse(res, true, 'Category created successfully', category, null, null, 201);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to create category', null, error);
    }
  }

  static async getCategory(req, res) {
    try {
      const query = req.query;
      const category = await CategoryService.findOne(req.params.id, query);

      if (!category) {
        return sendResponse(res, false, 'Category not found', null, null, null, 404);
      }

      return sendResponse(res, true, 'Category fetched successfully', category);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to fetch category', null, error);
    }
  }

  static async getCategoryWithSubcategories(req, res) {
    try {
      const category = await CategoryService.getWithSubcategories(req.params.id);

      if (!category) {
        return sendResponse(res, false, 'Category not found', null, null, null, 404);
      }

      return sendResponse(res, true, 'Category with subcategories fetched successfully', category);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to fetch category with subcategories', null, error);
    }
  }

  static async getAllCategories(req, res) {
    try {
      // Parse pagination parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {
        search: req.query.search,
        featured: req.query.featured,
        premium: req.query.premium,
        sort_by: req.query.sort_by || 'sort_order',
        sort_order: req.query.sort_order || 'asc',
        include_deleted: req.query.include_deleted || false
      };

      const result = await CategoryService.getAllPaginated(filters, page, limit);

      return sendResponse(res, true, 'Categories fetched successfully', result.data, null, result.meta);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to fetch categories', null, error);
    }
  }

  static async searchCategories(req, res) {
    try {
      const { name } = req.query;
      if (!name) {
        return sendResponse(res, false, 'Search query parameter "name" is required', null, null, null, 400);
      }

      const results = await CategoryService.searchByName(name);
      return sendResponse(res, true, 'Categories search completed', results, null, {
        count: results.length
      });
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to search categories', null, error);
    }
  }

  static async updateCategory(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = {};
      errors.array().forEach(error => {
        if (!formattedErrors[error.path]) {
          formattedErrors[error.path] = [];
        }
        formattedErrors[error.path] = error.msg;
      });

      return sendResponse(res, false, 'Validation failed', null, formattedErrors, null, 422);
    }

    try {
      const parsedData = CategoryController.parseCategoryFormData(req.body);
      const files = req.files;

      if (files?.image?.[0]) {
        parsedData.image = saveBufferToFile(files.image[0].buffer, files.image[0].originalname, 'uploads/categories');
      }

      if (files?.thumbnail?.[0]) {
        parsedData.thumbnail = saveBufferToFile(files.thumbnail[0].buffer, files.thumbnail[0].originalname, 'uploads/categories');
      }

      const category = await CategoryService.update(req.params.id, parsedData);

      if (!category) {
        return sendResponse(res, false, 'Category not found', null, null, null, 404);
      }

      return sendResponse(res, true, 'Category updated successfully', category);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to update category', null, error);
    }
  }

  static async deleteCategory(req, res) {
    try {
      const category = await CategoryService.delete(req.params.id);

      if (!category) {
        return sendResponse(res, false, 'Category not found', null, null, null, 404);
      }

      return sendResponse(res, true, 'Category deleted successfully');
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to delete category', null, error);
    }
  }
}

module.exports = CategoryController;
