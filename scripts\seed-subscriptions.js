const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seedSubscriptions() {
  try {
    console.log('🌱 Seeding subscription plans...');

    // Check if subscriptions already exist
    const existingSubscriptions = await prisma.subscriptions.count();
    if (existingSubscriptions > 0) {
      console.log('✅ Subscription plans already exist. Skipping seed.');
      return;
    }

    // Default subscription plans
    const subscriptionPlans = [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        name: 'Free Plan',
        description: 'Basic plan with limited features',
        price: 0.00,
        duration_days: 30,
        max_requests: 5,
        max_offers: 5,
        max_orders: 5,
        is_active: true,
        is_featured: false,
        user_type: 'BOTH',
        features: {
          support: 'email',
          priority: 'low'
        }
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        name: 'Buyer Basic',
        description: 'Basic plan for buyers',
        price: 9.99,
        duration_days: 30,
        max_requests: 20,
        max_offers: null,
        max_orders: 10,
        is_active: true,
        is_featured: false,
        user_type: 'BUYER',
        features: {
          support: 'email',
          priority: 'medium'
        }
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        name: 'Buyer Premium',
        description: 'Premium plan for buyers',
        price: 29.99,
        duration_days: 30,
        max_requests: 100,
        max_offers: null,
        max_orders: 50,
        is_active: true,
        is_featured: true,
        user_type: 'BUYER',
        features: {
          support: 'phone',
          priority: 'high',
          analytics: true
        }
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        name: 'Seller Basic',
        description: 'Basic plan for sellers',
        price: 19.99,
        duration_days: 30,
        max_requests: null,
        max_offers: 30,
        max_orders: 20,
        is_active: true,
        is_featured: false,
        user_type: 'SELLER',
        features: {
          support: 'email',
          priority: 'medium',
          commission: '5%'
        }
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440005',
        name: 'Seller Premium',
        description: 'Premium plan for sellers',
        price: 49.99,
        duration_days: 30,
        max_requests: null,
        max_offers: 100,
        max_orders: 100,
        is_active: true,
        is_featured: true,
        user_type: 'SELLER',
        features: {
          support: 'phone',
          priority: 'high',
          analytics: true,
          commission: '3%'
        }
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440006',
        name: 'Enterprise',
        description: 'Unlimited plan for large businesses',
        price: 99.99,
        duration_days: 30,
        max_requests: null,
        max_offers: null,
        max_orders: null,
        is_active: true,
        is_featured: true,
        user_type: 'BOTH',
        features: {
          support: 'dedicated',
          priority: 'highest',
          analytics: true,
          custom_features: true
        }
      }
    ];

    // Insert subscription plans
    for (const plan of subscriptionPlans) {
      await prisma.subscriptions.create({
        data: plan
      });
      console.log(`✅ Created subscription plan: ${plan.name}`);
    }

    console.log('🎉 Successfully seeded subscription plans!');
    console.log(`📊 Total plans created: ${subscriptionPlans.length}`);

  } catch (error) {
    console.error('❌ Error seeding subscription plans:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
if (require.main === module) {
  seedSubscriptions()
    .then(() => {
      console.log('✅ Subscription seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Subscription seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedSubscriptions };
