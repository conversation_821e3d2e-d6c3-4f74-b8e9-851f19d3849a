/**
 * Test script to verify offers enhancement deployment
 * Run with: node scripts/test-offers-enhancement.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testDatabaseSchema() {
  console.log('🔍 Testing database schema...');
  
  try {
    // Test 1: Check if new columns exist in offers table
    console.log('✅ Testing offers table columns...');
    const offerColumns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'offers' 
      AND column_name IN ('offer_title', 'category_id', 'subcategory_id', 'short_description', 'discount', 'quantity', 'offer_type', 'offer_code')
      ORDER BY column_name;
    `;
    
    const expectedColumns = ['category_id', 'discount', 'offer_code', 'offer_title', 'offer_type', 'quantity', 'short_description', 'subcategory_id'];
    const foundColumns = offerColumns.map(col => col.column_name);
    
    console.log('Found columns:', foundColumns);
    
    for (const expectedCol of expectedColumns) {
      if (foundColumns.includes(expectedCol)) {
        console.log(`  ✅ ${expectedCol} - Found`);
      } else {
        console.log(`  ❌ ${expectedCol} - Missing`);
      }
    }

    // Test 2: Check if request_code column exists
    console.log('\n✅ Testing request_code column...');
    const requestCodeColumn = await prisma.$queryRaw`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'requests' AND column_name = 'request_code';
    `;
    
    if (requestCodeColumn.length > 0) {
      console.log('  ✅ request_code column exists');
    } else {
      console.log('  ❌ request_code column missing');
    }

    // Test 3: Check if offer_form_field_values table exists
    console.log('\n✅ Testing offer_form_field_values table...');
    const formFieldTable = await prisma.$queryRaw`
      SELECT table_name FROM information_schema.tables 
      WHERE table_name = 'offer_form_field_values';
    `;
    
    if (formFieldTable.length > 0) {
      console.log('  ✅ offer_form_field_values table exists');
    } else {
      console.log('  ❌ offer_form_field_values table missing');
    }

    // Test 4: Check if offer_type enum exists
    console.log('\n✅ Testing offer_type enum...');
    const offerTypeEnum = await prisma.$queryRaw`
      SELECT enumlabel FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'offer_type_enum')
      ORDER BY enumlabel;
    `;
    
    if (offerTypeEnum.length > 0) {
      console.log('  ✅ offer_type_enum exists with values:', offerTypeEnum.map(e => e.enumlabel));
    } else {
      console.log('  ❌ offer_type_enum missing');
    }

    // Test 5: Check foreign key constraints
    console.log('\n✅ Testing foreign key constraints...');
    const constraints = await prisma.$queryRaw`
      SELECT constraint_name, table_name 
      FROM information_schema.table_constraints 
      WHERE constraint_type = 'FOREIGN KEY' 
      AND constraint_name IN (
        'offers_category_id_fkey', 
        'offers_subcategory_id_fkey',
        'offer_form_field_values_offer_id_fkey',
        'offer_form_field_values_form_field_id_fkey'
      )
      ORDER BY constraint_name;
    `;
    
    const expectedConstraints = [
      'offers_category_id_fkey',
      'offers_subcategory_id_fkey', 
      'offer_form_field_values_offer_id_fkey',
      'offer_form_field_values_form_field_id_fkey'
    ];
    
    const foundConstraints = constraints.map(c => c.constraint_name);
    
    for (const expectedConstraint of expectedConstraints) {
      if (foundConstraints.includes(expectedConstraint)) {
        console.log(`  ✅ ${expectedConstraint} - Found`);
      } else {
        console.log(`  ❌ ${expectedConstraint} - Missing`);
      }
    }

    console.log('\n🎉 Database schema test completed!');
    
  } catch (error) {
    console.error('❌ Database schema test failed:', error.message);
    throw error;
  }
}

async function testBasicQueries() {
  console.log('\n🔍 Testing basic database queries...');
  
  try {
    // Test 1: Query offers with new columns
    console.log('✅ Testing offers query...');
    const offers = await prisma.offers.findMany({
      take: 1,
      select: {
        id: true,
        offer_code: true,
        offer_title: true,
        category_id: true,
        subcategory_id: true,
        short_description: true,
        discount: true,
        quantity: true,
        offer_type: true,
        request_id: true
      }
    });
    console.log('  ✅ Offers query successful');

    // Test 2: Query requests with request_code
    console.log('✅ Testing requests query...');
    const requests = await prisma.requests.findMany({
      take: 1,
      select: {
        id: true,
        request_code: true,
        title: true
      }
    });
    console.log('  ✅ Requests query successful');

    // Test 3: Query offer_form_field_values
    console.log('✅ Testing offer_form_field_values query...');
    const formFieldValues = await prisma.offer_form_field_values.findMany({
      take: 1
    });
    console.log('  ✅ Offer form field values query successful');

    console.log('\n🎉 Basic queries test completed!');
    
  } catch (error) {
    console.error('❌ Basic queries test failed:', error.message);
    throw error;
  }
}

async function testModelRelations() {
  console.log('\n🔍 Testing model relations...');
  
  try {
    // Test offer relations
    console.log('✅ Testing offer relations...');
    const offerWithRelations = await prisma.offers.findFirst({
      include: {
        category: true,
        subcategory: true,
        offer_form_field_values: {
          include: {
            form_field: true
          }
        }
      }
    });
    console.log('  ✅ Offer relations query successful');

    console.log('\n🎉 Model relations test completed!');
    
  } catch (error) {
    console.error('❌ Model relations test failed:', error.message);
    // Don't throw here as relations might not have data yet
    console.log('  ⚠️  This is expected if no offers with categories exist yet');
  }
}

async function runTests() {
  console.log('🚀 Starting offers enhancement deployment tests...\n');
  
  try {
    await testDatabaseSchema();
    await testBasicQueries();
    await testModelRelations();
    
    console.log('\n✅ All tests passed! Offers enhancement deployment is successful.');
    
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testDatabaseSchema,
  testBasicQueries,
  testModelRelations,
  runTests
};
