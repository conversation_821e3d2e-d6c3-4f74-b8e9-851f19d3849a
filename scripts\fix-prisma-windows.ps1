# PowerShell script to fix Prisma permission issues on Windows
# Usage: PowerShell -ExecutionPolicy Bypass -File scripts/fix-prisma-windows.ps1

Write-Host "🔧 Fixing Prisma permission issues on Windows..." -ForegroundColor Yellow

# Step 1: Kill all Node.js processes
Write-Host "1. Stopping all Node.js processes..." -ForegroundColor Cyan
try {
    Get-Process node -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "   ✅ Node.js processes stopped" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  No Node.js processes found" -ForegroundColor Yellow
}

# Step 2: Wait a moment for processes to fully terminate
Start-Sleep -Seconds 2

# Step 3: Remove .prisma folder
Write-Host "2. Removing .prisma cache..." -ForegroundColor Cyan
if (Test-Path "node_modules\.prisma") {
    Remove-Item -Recurse -Force "node_modules\.prisma"
    Write-Host "   ✅ .prisma folder removed" -ForegroundColor Green
} else {
    Write-Host "   ⚠️  .prisma folder not found" -ForegroundColor Yellow
}

# Step 4: Remove @prisma folder (if needed)
if (Test-Path "node_modules\@prisma") {
    Write-Host "3. Removing @prisma cache..." -ForegroundColor Cyan
    Remove-Item -Recurse -Force "node_modules\@prisma"
    Write-Host "   ✅ @prisma folder removed" -ForegroundColor Green
}

# Step 5: Generate Prisma client
Write-Host "4. Generating Prisma client..." -ForegroundColor Cyan
try {
    & npx prisma generate
    Write-Host "   ✅ Prisma client generated successfully" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Failed to generate Prisma client" -ForegroundColor Red
    Write-Host "   Trying with force reset..." -ForegroundColor Yellow
    & npx prisma generate --force-reset
}

Write-Host "🎉 Prisma fix completed!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 To prevent future issues:" -ForegroundColor Cyan
Write-Host "   - Always close VS Code before running Prisma commands" -ForegroundColor White
Write-Host "   - Run this script when you encounter permission errors" -ForegroundColor White
Write-Host "   - Use npm run prisma:fix for quick access" -ForegroundColor White
