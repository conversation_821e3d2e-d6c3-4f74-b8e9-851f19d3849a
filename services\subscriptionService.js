const SubscriptionModel = require('../models/subscriptionModel');
const ApiError = require('../utils/apiError');

class SubscriptionService {
  /**
   * Create a new subscription plan (Admin only)
   * @param {Object} data - Subscription data
   * @returns {Promise<Object>} Created subscription
   */
  static async createSubscription(data) {
    try {
      // Validate required fields
      if (!data.name || !data.price || !data.duration_days) {
        throw new ApiError(400, 'Name, price, and duration_days are required');
      }

      // Validate user_type
      if (data.user_type && !['BUYER', 'SELLER', 'BOTH'].includes(data.user_type)) {
        throw new ApiError(400, 'Invalid user_type. Must be BUYER, SELLER, or BOTH');
      }

      // Validate limits (must be positive integers or null)
      if (data.max_requests !== null && data.max_requests !== undefined && data.max_requests < 0) {
        throw new ApiError(400, 'max_requests must be a positive integer or null for unlimited');
      }
      if (data.max_offers !== null && data.max_offers !== undefined && data.max_offers < 0) {
        throw new ApiError(400, 'max_offers must be a positive integer or null for unlimited');
      }
      if (data.max_orders !== null && data.max_orders !== undefined && data.max_orders < 0) {
        throw new ApiError(400, 'max_orders must be a positive integer or null for unlimited');
      }

      return await SubscriptionModel.createSubscription(data);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to create subscription: ${error.message}`);
    }
  }

  /**
   * Get all subscription plans
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated subscriptions
   */
  static async getAllSubscriptions(filters = {}, page = 1, limit = 10) {
    try {
      return await SubscriptionModel.getAllSubscriptions(filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get subscriptions: ${error.message}`);
    }
  }

  /**
   * Get subscription by ID
   * @param {string} id - Subscription ID
   * @returns {Promise<Object>} Subscription
   */
  static async getSubscriptionById(id) {
    try {
      const subscription = await SubscriptionModel.getSubscriptionById(id);
      if (!subscription) {
        throw new ApiError(404, 'Subscription not found');
      }
      return subscription;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to get subscription: ${error.message}`);
    }
  }

  /**
   * Update subscription plan (Admin only)
   * @param {string} id - Subscription ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated subscription
   */
  static async updateSubscription(id, data) {
    try {
      // Check if subscription exists
      await this.getSubscriptionById(id);

      // Validate user_type if provided
      if (data.user_type && !['BUYER', 'SELLER', 'BOTH'].includes(data.user_type)) {
        throw new ApiError(400, 'Invalid user_type. Must be BUYER, SELLER, or BOTH');
      }

      // Validate limits if provided
      if (data.max_requests !== null && data.max_requests !== undefined && data.max_requests < 0) {
        throw new ApiError(400, 'max_requests must be a positive integer or null for unlimited');
      }
      if (data.max_offers !== null && data.max_offers !== undefined && data.max_offers < 0) {
        throw new ApiError(400, 'max_offers must be a positive integer or null for unlimited');
      }
      if (data.max_orders !== null && data.max_orders !== undefined && data.max_orders < 0) {
        throw new ApiError(400, 'max_orders must be a positive integer or null for unlimited');
      }

      return await SubscriptionModel.updateSubscription(id, data);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to update subscription: ${error.message}`);
    }
  }

  /**
   * Delete subscription plan (Admin only)
   * @param {string} id - Subscription ID
   * @returns {Promise<Object>} Deleted subscription
   */
  static async deleteSubscription(id) {
    try {
      // Check if subscription exists
      await this.getSubscriptionById(id);

      return await SubscriptionModel.deleteSubscription(id);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to delete subscription: ${error.message}`);
    }
  }

  /**
   * Subscribe user to a plan
   * @param {string} userId - User ID
   * @param {string} subscriptionId - Subscription ID
   * @param {Object} paymentData - Payment information
   * @returns {Promise<Object>} User subscription
   */
  static async subscribeUser(userId, subscriptionId, paymentData = {}) {
    try {
      // Validate subscription exists and is active
      const subscription = await this.getSubscriptionById(subscriptionId);
      if (!subscription.is_active) {
        throw new ApiError(400, 'Subscription plan is not active');
      }

      return await SubscriptionModel.subscribeUser(userId, subscriptionId, paymentData);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to subscribe user: ${error.message}`);
    }
  }

  /**
   * Get user's active subscription
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Active subscription
   */
  static async getUserActiveSubscription(userId) {
    try {
      return await SubscriptionModel.getUserActiveSubscription(userId);
    } catch (error) {
      throw new ApiError(500, `Failed to get user subscription: ${error.message}`);
    }
  }

  /**
   * Get user's subscription history
   * @param {string} userId - User ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Subscription history
   */
  static async getUserSubscriptionHistory(userId, page = 1, limit = 10) {
    try {
      return await SubscriptionModel.getUserSubscriptionHistory(userId, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get subscription history: ${error.message}`);
    }
  }

  /**
   * Cancel user subscription
   * @param {string} userId - User ID
   * @param {string} userSubscriptionId - User subscription ID
   * @returns {Promise<Object>} Updated subscription
   */
  static async cancelSubscription(userId, userSubscriptionId) {
    try {
      // Verify the subscription belongs to the user
      const userSubscription = await SubscriptionModel.getUserActiveSubscription(userId);
      if (!userSubscription || userSubscription.id !== userSubscriptionId) {
        throw new ApiError(403, 'You can only cancel your own subscription');
      }

      return await SubscriptionModel.cancelSubscription(userSubscriptionId);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to cancel subscription: ${error.message}`);
    }
  }

  /**
   * Check if user can perform action and track usage
   * @param {string} userId - User ID
   * @param {string} usageType - Usage type (REQUEST, OFFER, ORDER)
   * @returns {Promise<Object>} Usage check result
   */
  static async checkAndTrackUsage(userId, usageType) {
    try {
      // Check if user can perform the action
      const usageCheck = await SubscriptionModel.checkUsageLimit(userId, usageType);
      
      if (!usageCheck.canUse) {
        throw new ApiError(403, usageCheck.reason);
      }

      // Track the usage
      await SubscriptionModel.trackUsage(userId, usageType);

      return {
        success: true,
        message: 'Action allowed and usage tracked',
        usage: usageCheck
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to check usage: ${error.message}`);
    }
  }

  /**
   * Get user's current usage status
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Usage status
   */
  static async getUserUsageStatus(userId) {
    try {
      const activeSubscription = await SubscriptionModel.getUserActiveSubscription(userId);
      
      if (!activeSubscription) {
        return {
          hasActiveSubscription: false,
          subscription: null,
          usage: null
        };
      }

      const [requestUsage, offerUsage, orderUsage] = await Promise.all([
        SubscriptionModel.checkUsageLimit(userId, 'REQUEST'),
        SubscriptionModel.checkUsageLimit(userId, 'OFFER'),
        SubscriptionModel.checkUsageLimit(userId, 'ORDER')
      ]);

      return {
        hasActiveSubscription: true,
        subscription: activeSubscription.subscription,
        subscriptionDetails: {
          id: activeSubscription.id,
          status: activeSubscription.status,
          start_date: activeSubscription.start_date,
          end_date: activeSubscription.end_date,
          auto_renew: activeSubscription.auto_renew
        },
        usage: {
          requests: requestUsage,
          offers: offerUsage,
          orders: orderUsage
        }
      };
    } catch (error) {
      throw new ApiError(500, `Failed to get usage status: ${error.message}`);
    }
  }

  /**
   * Get subscription statistics (Admin only)
   * @returns {Promise<Object>} Statistics
   */
  static async getSubscriptionStats() {
    try {
      return await SubscriptionModel.getSubscriptionStats();
    } catch (error) {
      throw new ApiError(500, `Failed to get subscription stats: ${error.message}`);
    }
  }

  /**
   * Get available subscription plans for user type
   * @param {string} userType - User type (BUYER, SELLER)
   * @returns {Promise<Object>} Available subscriptions
   */
  static async getAvailableSubscriptions(userType) {
    try {
      const filters = {
        user_type: userType,
        is_active: true
      };

      const result = await SubscriptionModel.getAllSubscriptions(filters, 1, 100);
      return result.data;
    } catch (error) {
      throw new ApiError(500, `Failed to get available subscriptions: ${error.message}`);
    }
  }
}

module.exports = SubscriptionService;
