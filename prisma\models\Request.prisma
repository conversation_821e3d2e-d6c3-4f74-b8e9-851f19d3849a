model requests {
  id              String   @id @default(uuid())
  request_code    String?  @unique
  buyer_id        String
  category_id     String
  sub_category_id String
  title           String   @unique
  short_description     String?
  description     String?
  quantity        Int      @default(1)
  budget_min      Float?   @default(0)
  budget_max      Float?   @default(0)
  deadline        DateTime?
  urgency         String   @default("Normal")
  status          String   @default("Pending")
  request_type    String   @default("General")
  location        String?
  additional_info String?
  file            String?
  service_period  Int?
  session_count   Int?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean  @default(false)

  // Relations
  buyer          users      @relation(fields: [buyer_id], references: [id], name: "Buyer")
  category       categories  @relation(fields: [category_id], references: [id], name: "RequestCategory")
  sub_category   sub_categories @relation(fields: [sub_category_id], references: [id], name: "RequestSubCategory")


  // Child relations
  request_attachments   request_attachments[]
  merged_requests         request_merged_items[] @relation("MergedItem")
  request_statuses         request_statuses[] @relation("RequestStatusUpdates")
  offers         offers[] @relation("RequestOffer")
  assigned_sellers      request_assigned_sellers[] @relation("RequestAssignedSeller")

}




model request_attachments {
  id              String  @id @default(uuid())
  request_id      String
  file_path       String
  file_type       String
  file_size       Int?
  description     String?
  is_public       Boolean @default(false)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean @default(false)

  // Relations
  request         requests  @relation(fields: [request_id], references: [id])
}

model request_statuses {
  id              String   @id @default(uuid())
  request_id      String
  updated_by      String   // User who changed the status
  status          String   @default("Pending")
  reason          String?  // Reason for status change (e.g., "Merged with another request")
  previous_status String?  // Stores the last status before update
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  request         requests  @relation(fields: [request_id], references: [id], name: "RequestStatusUpdates")
  updated_by_user users     @relation(fields: [updated_by], references: [id], name: "RequestUpdator")
}


model request_merged_items {
  id              String   @id @default(uuid())
  request_id      String
  merged_item_id  String
  merged_by       String
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  merged_item     requests  @relation(name: "MergedItem", fields: [merged_item_id], references: [id])
  merged_by_user   users     @relation(fields: [merged_by], references: [id], name: "MergedByAdmin")
}

model request_assigned_sellers {
  id          String   @id @default(uuid())
  request_id  String
  seller_id   String
  assigned_at DateTime @default(now())
  assigned_by String?  // User ID who assigned this seller
  status      String   @default("Pending") // e.g., "Pending", "Accepted", "Rejected"
  notes       String?

  // Relations
  request     requests @relation(fields: [request_id], references: [id], name: "RequestAssignedSeller")
  seller      users    @relation(fields: [seller_id], references: [id], name: "AssignedSeller")
  assigner    users?   @relation(fields: [assigned_by], references: [id], name: "SellerAssigner")

  @@unique([request_id, seller_id]) // Ensure a seller is only assigned once per request
}


