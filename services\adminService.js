const UserModel = require('../models/userModel');
const CategoryModel = require('../models/categoryModel');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const ApiError = require('../utils/apiError');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class AdminService {
  async login(email, password) {

    const user = await UserModel.findUserByEmail(email);

    if (!user) {
      throw new ApiError(401, 'User not found');
    }

    if (!user.roles.includes('Admin')) {
      throw new ApiError(403, 'Access denied. Only users with Admin role can log in to the admin portal.');
    }


    const isPasswordValid = await UserModel.comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw new ApiError(401, 'Invalid password');
    }

    const accessToken = jwt.sign(
      { id: user.id, email: user.email, roles: user.roles },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    const refreshToken = jwt.sign(
      { id: user.id },
      process.env.REFRESH_SECRET,
      { expiresIn: '7d' }
    );

    await UserModel.storeRefreshToken(user.id, refreshToken);

    return { accessToken, refreshToken, user };
  }

  async refreshToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.REFRESH_SECRET);
      const user = await UserModel.findUserById(decoded.id);

      if (!user) throw new Error("Invalid refresh token");

      const newAccessToken = jwt.sign(
        { id: user.id, email: user.email, roles: user.roles },
        process.env.JWT_SECRET,
        { expiresIn: "7d" }
      );

      return { accessToken: newAccessToken };
    } catch (error) {
      throw new Error("Invalid or expired refresh token");
    }
  }

  async getAllUsers(page = 1, limit = 10, role, search = '', status = null, isApproved = null) {
    try {
      return await UserModel.getAllUsers(page, limit, role, search, status, isApproved);
    } catch (err) {
      throw new Error('Error fetching users: ' + err.message);
    }
  }

  async getUserById(userId) {
    const user = await UserModel.findUserById(userId);
    if (!user) throw new Error('User not found');
    return user;
  }

  async getUserByIdWithRoles(userId) {
    const user = await UserModel.findUserByIdWithRoles(userId);
    if (!user) throw new Error('User not found');
    return user;
  }

  async updateUserRole(userId, roles, adminId) {
    try {
      return await UserModel.updateUserRole(userId, roles, adminId);
    } catch (error) {
      console.error('Error updating user role:', error);
      throw new ApiError(500, error.message || 'Failed to update user role');
    }
  }

  /**
   * Update user data
   * @param {string} userId - User ID
   * @param {Object} userData - User data to update
   * @param {string} adminId - Admin ID
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userId, userData, adminId) {
    try {
      // Check if user exists
      const existingUser = await UserModel.findUserById(userId);
      if (!existingUser) {
        throw new ApiError(404, 'User not found');
      }

      console.log('Updating user with data:', userData);

      // Handle role update separately if provided
      let updatedUser;
      if (userData.role) {
        const roles = [userData.role];
        delete userData.role; // Remove role from userData to avoid conflicts

        // First update other user data
        if (Object.keys(userData).length > 0) {
          updatedUser = await UserModel.updateUser(userId, userData);
        }

        // Then update roles
        updatedUser = await UserModel.updateUserRole(userId, roles, adminId);
      } else {
        // Just update user data
        updatedUser = await UserModel.updateUser(userId, userData);

        // Transform roles to simple array for the response if they exist
        if (updatedUser.roles && Array.isArray(updatedUser.roles)) {
          updatedUser.roles = updatedUser.roles.map(userRole => userRole.role.name);
        }
      }

      // Log activity
      if (adminId) {
        await this.createActivityLog(
          adminId,
          userId,
          'User Updated',
          `Admin updated user: ${existingUser.email}`
        );
      }

      return updatedUser;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('Error updating user:', error);
      throw new ApiError(500, error.message || 'Failed to update user');
    }
  }

  async deleteUser(userId, adminId) {
    return await UserModel.deleteUser(userId, adminId);
  }

  async getAllRoles() {
    return await UserModel.getAllRoles();
  }

  async updateRole(roleId, roleName, adminId) {
    return await UserModel.updateRole(roleId, roleName, adminId);
  }

  async getActivityLogs() {
    return await UserModel.getActivityLogs();
  }

  async createActivityLog(adminId, userId, action, details) {
    await UserModel.createActivityLog(adminId, userId, action, details);
  }

  /**
   * Create a new user with specified role
   * @param {Object} userData - User data including role
   * @param {String} adminId - ID of the admin creating the user
   * @returns {Promise<Object>} Created user object
   */
  async createUser(userData, adminId) {
    const { email, phone_number, role } = userData;

    // Check if user with email exists
    const existingUserByEmail = await UserModel.findUserByEmail(email);
    if (existingUserByEmail) {
      const error = new ApiError(422, 'The email has already been taken.');
      error.field = 'email';
      throw error;
    }

    // Check if user with phone exists
    const existingUserByPhone = await UserModel.findUserByPhone(phone_number);
    if (existingUserByPhone) {
      const error = new ApiError(422, 'The phone number has already been taken.');
      error.field = 'phone_number';
      throw error;
    }

    // Check if role exists
    const roleExists = await prisma.roles.findUnique({
      where: { name: role }
    });

    if (!roleExists) {
      const error = new ApiError(422, `The selected role is invalid.`);
      error.field = 'role';
      throw error;
    }

    try {
      // Create the user
      userData.status = "active";
      userData.is_approved = true;
      const user = await UserModel.createUser(userData);

      // Log activity
      await this.createActivityLog(
        adminId,
        user.id,
        'User Created',
        `Admin created a new ${role} user: ${email}`
      );

      // Transform roles to simple array for response
      if (user.roles) {
        user.roles = user.roles.map(userRole => userRole.role.name);
      }

      return user;
    } catch (error) {
      console.error('Error creating user:', error);

      // Check for specific database errors and format them in a Laravel-style
      if (error.code === 'P2002') {
        // This is a Prisma unique constraint violation
        const field = error.meta?.target?.[0] || 'field';
        const fieldError = new ApiError(422, `The ${field} has already been taken.`);
        fieldError.field = field;
        throw fieldError;
      } else if (error.message.includes('validation')) {
        // This might be a validation error from somewhere else
        const fieldMatch = error.message.match(/validation failed for field '(\w+)'/i);
        if (fieldMatch && fieldMatch[1]) {
          const fieldError = new ApiError(422, error.message);
          fieldError.field = fieldMatch[1];
          throw fieldError;
        }
      } else if (error.message.includes('already') || error.message.includes('invalid')) {
        // Other validation-like errors
        const fieldError = new ApiError(422, error.message);
        fieldError.field = 'general';
        throw fieldError;
      }

      // Generic error
      throw new ApiError(500, 'Failed to create user: ' + error.message);
    }
  }

  /**
   * Update user password
   * @param {string} userId - User ID
   * @param {string} newPassword - New password
   * @param {string} adminId - Admin ID
   * @returns {Promise<Object>} Updated user
   */
  async updateUserPassword(userId, newPassword, adminId) {
    try {
      // Check if user exists
      const existingUser = await UserModel.findUserById(userId);
      if (!existingUser) {
        throw new ApiError(404, 'User not found');
      }

      // Hash the new password
      const saltRounds = 12;
      const password_hash = await bcrypt.hash(newPassword, saltRounds);

      // Update user password
      const updatedUser = await UserModel.updateUser(userId, { password_hash });

      // Log activity
      if (adminId) {
        await this.createActivityLog(
          adminId,
          userId,
          'Password Updated',
          `Admin updated password for user: ${existingUser.email}`
        );
      }

      // Remove sensitive data from response
      delete updatedUser.password_hash;

      return updatedUser;

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('Error updating user password:', error);
      throw new ApiError(500, error.message || 'Failed to update user password');
    }
  }
}

module.exports = new AdminService();