const express = require('express');
const userController = require('../controllers/userController');
const requestController = require('../controllers/requestController');
const buyerController = require('../controllers/buyerController');
const cartController = require('../controllers/cartController');
const orderController = require('../controllers/orderController');
const CategoryController = require('../controllers/categoryController');
const subscriptionController = require('../controllers/subscriptionController');
const {
  subscribeUserValidation,
  cancelSubscriptionValidation,
  queryValidation
} = require('../validations/subscriptionValidation');
const authenticateToken = require('../middlewares/authMiddleware');
const authorizeRoles = require('../middlewares/authorizeMiddleware');
const createRequestValidation = require('../validations/Request/createValidation');
const updateProfileValidation = require('../validations/Buyer/updateProfileValidation');
const { addToCartValidation, updateCartItemValidation } = require('../validations/Cart/cartValidation');
const confirmOrderValidation = require('../validations/Order/confirmOrderValidation');


const { uploadRequestFiles, uploadAvatarUser } = require('../middlewares/upload');
const { checkRequestLimit, checkOrderLimit } = require('../middlewares/subscriptionMiddleware');

const router = express.Router();

router.get('/users', authenticateToken, authorizeRoles(['Buyer']), userController.getUsers);
router.get('/users/search', authenticateToken, authorizeRoles(['Buyer']), userController.searchUsers);
router.get('/profile', authenticateToken, authorizeRoles(['Buyer']), userController.getUserProfile);
router.get('/users/:id', authenticateToken, authorizeRoles(['Buyer']), userController.getUserById);
router.post('/users', authenticateToken, authorizeRoles(['Buyer']), userController.createUser);
router.put('/users/:id', authenticateToken, authorizeRoles(['Buyer']), userController.updateUser);
router.patch('/users/:id', authenticateToken, authorizeRoles(['Buyer']), userController.partialUpdateUser);
router.delete('/users/:id', authenticateToken, authorizeRoles(['Buyer']), userController.deleteUser);

// Buyer profile routes
router.get('/profile', authenticateToken, authorizeRoles(['Buyer']), buyerController.getBuyerProfile);
router.put('/profile', authenticateToken, authorizeRoles(['Buyer']), uploadAvatarUser, updateProfileValidation, buyerController.updateBuyerProfile);
router.get('/my-requests', authenticateToken, authorizeRoles(['Buyer']), buyerController.getBuyerRequests);
router.get('/my-offers', authenticateToken, authorizeRoles(['Buyer']), buyerController.getBuyerOffers);
router.get('/processed-offers', authenticateToken, authorizeRoles(['Buyer']), buyerController.getBuyerAcceptedOffers);
router.get('/offers/:id', authenticateToken, authorizeRoles(['Buyer']), buyerController.getBuyerOfferById);

router.get('/categories', authenticateToken, authorizeRoles(['Buyer']), CategoryController.getAllCategories);
router.get('/categories/:id/subcategories', authenticateToken, authorizeRoles(['Buyer']), CategoryController.getCategoryWithSubcategories);
router.get('/subcategories/:id', authenticateToken, authorizeRoles(['Buyer']), buyerController.getSubcategoryWithFormFields);

router.get('/requests', authenticateToken, authorizeRoles(['Buyer']), requestController.getRequests);
router.get('/requests/:id', authenticateToken, authorizeRoles(['Buyer']), requestController.getRequestById);
router.get('/requests/:id/status-history', authenticateToken, authorizeRoles(['Buyer']), requestController.getRequestStatusHistory);
router.post('/requests', authenticateToken, authorizeRoles(['Buyer']), checkRequestLimit, uploadRequestFiles, createRequestValidation, requestController.createRequest);
router.put('/requests/:id', authenticateToken, authorizeRoles(['Buyer']), uploadRequestFiles, requestController.updateRequest);
router.post('/requests/:id/cancel', authenticateToken, authorizeRoles(['Buyer']), requestController.cancelRequest);
router.delete('/requests/:id', authenticateToken, authorizeRoles(['Buyer']), requestController.deleteRequest);
router.delete('/requests/attachments/:attachmentId', authenticateToken, authorizeRoles(['Buyer']), requestController.deleteAttachment);

// Cart routes
router.post('/cart', authenticateToken, authorizeRoles(['Buyer']), addToCartValidation, cartController.addToCart);
router.get('/cart', authenticateToken, authorizeRoles(['Buyer']), cartController.getCartItems);
router.put('/cart/:itemId', authenticateToken, authorizeRoles(['Buyer']), updateCartItemValidation, cartController.updateCartItem);
router.delete('/cart/:itemId', authenticateToken, authorizeRoles(['Buyer']), cartController.removeCartItem);
router.delete('/cart', authenticateToken, authorizeRoles(['Buyer']), cartController.clearCart);

// Order routes
router.post('/orders/confirm', authenticateToken, authorizeRoles(['Buyer']), checkOrderLimit, confirmOrderValidation, orderController.confirmOrder);
router.get('/orders', authenticateToken, authorizeRoles(['Buyer']), orderController.getBuyerOrders);
router.get('/orders/:id', authenticateToken, authorizeRoles(['Buyer']), orderController.getBuyerOrderDetails);

// Subscription Routes for Buyers
router.get('/subscriptions/available', authenticateToken, authorizeRoles(['Buyer']), subscriptionController.getAvailableSubscriptions);
router.post('/subscriptions/subscribe', authenticateToken, authorizeRoles(['Buyer']), subscribeUserValidation, subscriptionController.subscribeUser);
router.get('/subscriptions/active', authenticateToken, authorizeRoles(['Buyer']), subscriptionController.getUserActiveSubscription);
router.get('/subscriptions/history', authenticateToken, authorizeRoles(['Buyer']), queryValidation, subscriptionController.getUserSubscriptionHistory);
router.get('/subscriptions/usage', authenticateToken, authorizeRoles(['Buyer']), subscriptionController.getUserUsageStatus);
router.put('/subscriptions/:subscription_id/cancel', authenticateToken, authorizeRoles(['Buyer']), cancelSubscriptionValidation, subscriptionController.cancelSubscription);

module.exports = router;

