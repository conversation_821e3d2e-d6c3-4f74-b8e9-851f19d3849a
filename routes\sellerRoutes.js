const express = require('express');
const userController = require('../controllers/userController');
const requestController = require('../controllers/requestController');
const sellerController = require('../controllers/sellerController');
const sellerInterestController = require('../controllers/sellerInterestController');
const businessInformationController = require('../controllers/businessInformationController');
const CategoryController = require('../controllers/categoryController');
const subscriptionController = require('../controllers/subscriptionController');
const {
  subscribeUserValidation,
  cancelSubscriptionValidation,
  queryValidation
} = require('../validations/subscriptionValidation');
const authenticateToken = require('../middlewares/authMiddleware');
const authorizeRoles = require('../middlewares/authorizeMiddleware');
const createRequestValidation = require('../validations/Request/createValidation');
const createOfferValidation = require('../validations/Seller/createOfferValidation');
const updateOfferValidation = require('../validations/Seller/updateOfferValidation');
const rejectRequestValidation = require('../validations/Seller/rejectRequestValidation');
const updateProfileValidation = require('../validations/Seller/updateProfileValidation');
const {
  addCategoryInterestValidation,
  addSubcategoryInterestValidation,
  updateCategoriesValidation,
  updateSubcategoriesValidation
} = require('../validations/Seller/interestValidation');
const {
  createBusinessInformationValidation,
  updateBusinessInformationValidation,
  getBusinessInformationValidation,
  getBusinessInformationsValidation
} = require('../validations/businessInformationValidation');

const { uploadRequestFiles, uploadAvatarUser, uploadBusinessFiles } = require('../middlewares/upload');
const { checkOfferLimit } = require('../middlewares/subscriptionMiddleware');

const router = express.Router();

router.get('/users', authenticateToken, userController.getUsers);
router.get('/users/search', authenticateToken, userController.searchUsers);
router.get('/profile', authenticateToken,  authorizeRoles(['Seller']), userController.getUserProfile);
router.get('/users/:id', authenticateToken, userController.getUserById);
router.post('/users', authenticateToken, userController.createUser);
router.put('/users/:id', authenticateToken, userController.updateUser);
router.patch('/users/:id', authenticateToken, userController.partialUpdateUser);
router.delete('/users/:id', authenticateToken, userController.deleteUser);
router.get('/profile', authenticateToken, (req, res) => {
  res.json({ message: `Hello Mr, ${req.user.name}` });
});

router.get('/categories', authenticateToken, CategoryController.getAllCategories);


// Seller assigned requests routes
router.get('/assigned-requests', authenticateToken, authorizeRoles(['Seller']), sellerController.getAssignedRequests);
router.post('/assigned-requests/:assignmentId/accept', authenticateToken, authorizeRoles(['Seller']), sellerController.acceptAssignedRequest);
router.post('/assigned-requests/:assignmentId/reject', authenticateToken, authorizeRoles(['Seller']), rejectRequestValidation, sellerController.rejectAssignedRequest);
router.get('/requests/:requestId', authenticateToken, authorizeRoles(['Seller']), sellerController.getRequestDetailsForSeller);

// Seller offers routes
router.post('/offers', authenticateToken, authorizeRoles(['Seller']), checkOfferLimit, createOfferValidation, sellerController.createOffer);
router.get('/offers', authenticateToken, authorizeRoles(['Seller']), sellerController.getSellerOffers);
router.put('/offers/:offerId', authenticateToken, authorizeRoles(['Seller']), updateOfferValidation, sellerController.updateOffer);

// Seller profile routes
router.put('/profile', authenticateToken, authorizeRoles(['Seller']), uploadAvatarUser, updateProfileValidation, sellerController.updateProfile);

// Seller interested categories routes
router.get('/interested-categories', authenticateToken, authorizeRoles(['Seller']), sellerInterestController.getInterestedCategories);
router.post('/interested-categories', authenticateToken, authorizeRoles(['Seller']), addCategoryInterestValidation, sellerInterestController.addInterestedCategory);
router.delete('/interested-categories/:categoryId', authenticateToken, authorizeRoles(['Seller']), sellerInterestController.removeInterestedCategory);
router.put('/interested-categories', authenticateToken, authorizeRoles(['Seller']), updateCategoriesValidation, sellerInterestController.updateInterestedCategories);

// Seller interested subcategories routes
router.get('/interested-subcategories', authenticateToken, authorizeRoles(['Seller']), sellerInterestController.getInterestedSubcategories);
router.post('/interested-subcategories', authenticateToken, authorizeRoles(['Seller']), addSubcategoryInterestValidation, sellerInterestController.addInterestedSubcategory);
router.delete('/interested-subcategories/:subcategoryId', authenticateToken, authorizeRoles(['Seller']), sellerInterestController.removeInterestedSubcategory);
router.put('/interested-subcategories', authenticateToken, authorizeRoles(['Seller']), updateSubcategoriesValidation, sellerInterestController.updateInterestedSubcategories);

// Get subcategories for categories that the seller is interested in
router.get('/subcategories-for-interested-categories', authenticateToken, authorizeRoles(['Seller']), sellerInterestController.getSubcategoriesForInterestedCategories);

// Business Information routes
router.post('/business-information', authenticateToken, authorizeRoles(['Seller']), uploadBusinessFiles, createBusinessInformationValidation, businessInformationController.createBusinessInformation);
router.get('/business-information', authenticateToken, authorizeRoles(['Seller']), getBusinessInformationsValidation, businessInformationController.getMyBusinessInformations);
router.get('/business-information/:id', authenticateToken, authorizeRoles(['Seller']), getBusinessInformationValidation, businessInformationController.getBusinessInformationById);
router.put('/business-information/:id', authenticateToken, authorizeRoles(['Seller']), uploadBusinessFiles, updateBusinessInformationValidation, businessInformationController.updateBusinessInformation);
router.delete('/business-information/:id', authenticateToken, authorizeRoles(['Seller']), getBusinessInformationValidation, businessInformationController.deleteBusinessInformation);

// Subscription Routes for Sellers
router.get('/subscriptions/available', authenticateToken, authorizeRoles(['Seller']), subscriptionController.getAvailableSubscriptions);
router.post('/subscriptions/subscribe', authenticateToken, authorizeRoles(['Seller']), subscribeUserValidation, subscriptionController.subscribeUser);
router.get('/subscriptions/active', authenticateToken, authorizeRoles(['Seller']), subscriptionController.getUserActiveSubscription);
router.get('/subscriptions/history', authenticateToken, authorizeRoles(['Seller']), queryValidation, subscriptionController.getUserSubscriptionHistory);
router.get('/subscriptions/usage', authenticateToken, authorizeRoles(['Seller']), subscriptionController.getUserUsageStatus);
router.put('/subscriptions/:subscription_id/cancel', authenticateToken, authorizeRoles(['Seller']), cancelSubscriptionValidation, subscriptionController.cancelSubscription);

module.exports = router;

